import SwiftUI
import Combine
import Foundation

@MainActor
class AppState: ObservableObject {
    // MARK: - Published Properties
    
    @Published var isConnected: Bool = false
    @Published var connectionStatus: ConnectionStatus = .disconnected
    @Published var currentView: AppView = .dashboard
    
    // System Data
    @Published var systemMetrics: SystemMetrics?
    @Published var services: [ServiceInfo] = []
    @Published var dockerContainers: [DockerContainer] = []
    @Published var dockerImages: [DockerImage] = []
    @Published var logEntries: [LogEntry] = []
    @Published var notifications: [AppNotification] = []
    
    // UI State
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var showingQuickSearch: Bool = false
    @Published var showingSettings: Bool = false
    @Published var selectedService: ServiceInfo?
    @Published var selectedContainer: DockerContainer?
    
    // Settings
    @Published var settings: AppSettings
    
    // Search
    @Published var searchQuery: String = ""
    @Published var searchResults: [SearchResult] = []
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    private let apiClient: APIClient
    private let socketManager: SocketManager
    private let notificationManager: NotificationManager
    private let preferencesManager: PreferencesManager
    
    // MARK: - Initialization
    
    init() {
        let defaultSettings = AppSettings(
            connection: ConnectionSettings(
                serverURL: "http://localhost:3000",
                apiKey: nil,
                websocketURL: "ws://localhost:3000",
                autoConnect: true,
                connectionTimeout: 30,
                retryAttempts: 3
            ),
            notifications: NotificationSettings(
                systemAlerts: true,
                serviceEvents: true,
                dockerEvents: true,
                errorAlerts: true,
                soundEnabled: true,
                badgeEnabled: true
            ),
            appearance: AppearanceSettings(
                colorScheme: .system,
                accentColor: "blue",
                showMenuBar: true,
                compactMode: false
            ),
            monitoring: MonitoringSettings(
                refreshInterval: 5.0,
                historyRetention: 86400, // 24 hours
                alertThresholds: AlertThresholds(
                    cpuWarning: 70.0,
                    cpuCritical: 90.0,
                    memoryWarning: 80.0,
                    memoryCritical: 95.0,
                    diskWarning: 85.0,
                    diskCritical: 95.0
                ),
                enableRealtime: true
            ),
            shortcuts: ShortcutSettings(
                quickSearch: "⌘K",
                refreshAll: "⌘R",
                connectServer: "⌘⇧C",
                checkUpdates: "⌘⇧U",
                showMenuBar: "⌘⇧M"
            ),
            advanced: AdvancedSettings(
                debugMode: false,
                logLevel: .info,
                enableAnalytics: false,
                exportFormat: .json
            )
        )
        
        self.settings = defaultSettings
        self.apiClient = APIClient(baseURL: defaultSettings.connection.serverURL)
        self.socketManager = SocketManager(url: defaultSettings.connection.websocketURL ?? "")
        self.notificationManager = NotificationManager()
        self.preferencesManager = PreferencesManager()
        
        setupNotificationObservers()
        setupSearchBinding()
        loadSettings()
        
        if self.settings.connection.autoConnect {
            Task {
                await connectToServer()
            }
        }
    }
    
    // MARK: - Connection Management
    
    func connectToServer() async {
        connectionStatus = .connecting
        isLoading = true
        
        do {
            // Test API connection
            let response = await apiClient.testConnection()
            
            if response.success {
                isConnected = true
                connectionStatus = .connected
                
                // Connect WebSocket
                await socketManager.connect()
                
                // Start data refresh
                await refreshAllData()
                
                // Setup periodic refresh
                setupPeriodicRefresh()
                
                await showNotification(
                    title: "Connected",
                    message: "Successfully connected to WOW Monitor backend",
                    type: .success
                )
            } else {
                throw APIError.connectionFailed(response.error ?? "Unknown error")
            }
        } catch {
            connectionStatus = .error
            errorMessage = error.localizedDescription
            
            await showNotification(
                title: "Connection Failed",
                message: error.localizedDescription,
                type: .error
            )
        }
        
        isLoading = false
    }
    
    func disconnectFromServer() {
        isConnected = false
        connectionStatus = .disconnected
        socketManager.disconnect()
        cancelPeriodicRefresh()
    }
    
    // MARK: - Data Management
    
    func refreshAllData() async {
        guard isConnected else { return }
        
        isLoading = true
        
        async let metricsTask = loadSystemMetrics()
        async let servicesTask = loadServices()
        async let containersTask = loadDockerContainers()
        async let imagesTask = loadDockerImages()
        async let logsTask = loadRecentLogs()
        
        await metricsTask
        await servicesTask
        await containersTask
        await imagesTask
        await logsTask
        
        isLoading = false
    }
    
    private func loadSystemMetrics() async {
        do {
            let response = await apiClient.getSystemMetrics()
            if let metrics = response.data {
                systemMetrics = metrics
                checkAlertThresholds(metrics)
            }
        } catch {
            print("Failed to load system metrics: \(error)")
        }
    }
    
    private func loadServices() async {
        do {
            let response = await apiClient.getServices()
            if let serviceList = response.data {
                services = serviceList
            }
        } catch {
            print("Failed to load services: \(error)")
        }
    }
    
    private func loadDockerContainers() async {
        do {
            let response = await apiClient.getDockerContainers()
            if let containers = response.data {
                dockerContainers = containers
            }
        } catch {
            print("Failed to load Docker containers: \(error)")
        }
    }
    
    private func loadDockerImages() async {
        do {
            let response = await apiClient.getDockerImages()
            if let images = response.data {
                dockerImages = images
            }
        } catch {
            print("Failed to load Docker images: \(error)")
        }
    }
    
    private func loadRecentLogs() async {
        do {
            let response = await apiClient.getRecentLogs(limit: 100)
            if let logs = response.data {
                logEntries = logs
            }
        } catch {
            print("Failed to load recent logs: \(error)")
        }
    }
    
    // MARK: - Service Management
    
    func startService(_ serviceId: String) async {
        do {
            let response = await apiClient.startService(serviceId)
            if response.success {
                await refreshAllData()
                await showNotification(
                    title: "Service Started",
                    message: "Service \(serviceId) has been started",
                    type: .success
                )
            }
        } catch {
            await showNotification(
                title: "Failed to Start Service",
                message: error.localizedDescription,
                type: .error
            )
        }
    }
    
    func stopService(_ serviceId: String) async {
        do {
            let response = await apiClient.stopService(serviceId)
            if response.success {
                await refreshAllData()
                await showNotification(
                    title: "Service Stopped",
                    message: "Service \(serviceId) has been stopped",
                    type: .success
                )
            }
        } catch {
            await showNotification(
                title: "Failed to Stop Service",
                message: error.localizedDescription,
                type: .error
            )
        }
    }
    
    func restartService(_ serviceId: String) async {
        do {
            let response = await apiClient.restartService(serviceId)
            if response.success {
                await refreshAllData()
                await showNotification(
                    title: "Service Restarted",
                    message: "Service \(serviceId) has been restarted",
                    type: .success
                )
            }
        } catch {
            await showNotification(
                title: "Failed to Restart Service",
                message: error.localizedDescription,
                type: .error
            )
        }
    }
    
    // MARK: - Docker Management
    
    func startContainer(_ containerId: String) async {
        do {
            let response = await apiClient.startContainer(containerId)
            if response.success {
                await refreshAllData()
                await showNotification(
                    title: "Container Started",
                    message: "Container \(containerId) has been started",
                    type: .success
                )
            }
        } catch {
            await showNotification(
                title: "Failed to Start Container",
                message: error.localizedDescription,
                type: .error
            )
        }
    }
    
    func stopContainer(_ containerId: String) async {
        do {
            let response = await apiClient.stopContainer(containerId)
            if response.success {
                await refreshAllData()
                await showNotification(
                    title: "Container Stopped",
                    message: "Container \(containerId) has been stopped",
                    type: .success
                )
            }
        } catch {
            await showNotification(
                title: "Failed to Stop Container",
                message: error.localizedDescription,
                type: .error
            )
        }
    }
    
    func restartContainer(_ containerId: String) async {
        do {
            let response = await apiClient.restartContainer(containerId)
            if response.success {
                await refreshAllData()
                await showNotification(
                    title: "Container Restarted",
                    message: "Container \(containerId) has been restarted",
                    type: .success
                )
            }
        } catch {
            await showNotification(
                title: "Failed to Restart Container",
                message: error.localizedDescription,
                type: .error
            )
        }
    }
    
    // MARK: - Alert Management
    
    private func checkAlertThresholds(_ metrics: SystemMetrics) {
        let thresholds = settings.monitoring.alertThresholds
        
        // CPU Alerts
        if metrics.cpu.usage >= thresholds.cpuCritical {
            Task {
                await showNotification(
                    title: "Critical CPU Usage",
                    message: "CPU usage is at \(String(format: "%.1f", metrics.cpu.usage))%",
                    type: .error
                )
            }
        } else if metrics.cpu.usage >= thresholds.cpuWarning {
            Task {
                await showNotification(
                    title: "High CPU Usage",
                    message: "CPU usage is at \(String(format: "%.1f", metrics.cpu.usage))%",
                    type: .warning
                )
            }
        }
        
        // Memory Alerts
        if metrics.memory.usagePercentage >= thresholds.memoryCritical {
            Task {
                await showNotification(
                    title: "Critical Memory Usage",
                    message: "Memory usage is at \(String(format: "%.1f", metrics.memory.usagePercentage))%",
                    type: .error
                )
            }
        } else if metrics.memory.usagePercentage >= thresholds.memoryWarning {
            Task {
                await showNotification(
                    title: "High Memory Usage",
                    message: "Memory usage is at \(String(format: "%.1f", metrics.memory.usagePercentage))%",
                    type: .warning
                )
            }
        }
        
        // Disk Alerts
        if metrics.disk.usagePercentage >= thresholds.diskCritical {
            Task {
                await showNotification(
                    title: "Critical Disk Usage",
                    message: "Disk usage is at \(String(format: "%.1f", metrics.disk.usagePercentage))%",
                    type: .error
                )
            }
        } else if metrics.disk.usagePercentage >= thresholds.diskWarning {
            Task {
                await showNotification(
                    title: "High Disk Usage",
                    message: "Disk usage is at \(String(format: "%.1f", metrics.disk.usagePercentage))%",
                    type: .warning
                )
            }
        }
    }
    
    // MARK: - Notification Management
    
    private func showNotification(title: String, message: String, type: NotificationType) async {
        let notification = AppNotification(
            title: title,
            message: message,
            type: type,
            timestamp: Date(),
            read: false,
            actions: nil
        )
        
        notifications.insert(notification, at: 0)
        
        if settings.notifications.soundEnabled {
            await notificationManager.showNotification(
                title: title,
                message: message,
                type: type
            )
        }
    }
    
    func markNotificationAsRead(_ notificationId: UUID) {
        if let index = notifications.firstIndex(where: { $0.id == notificationId }) {
            notifications[index].read = true
        }
    }
    
    // MARK: - Settings Management
    
    func updateSettings(_ newSettings: AppSettings) {
        settings = newSettings
        saveSettings()
        
        // Update API client
        apiClient.updateBaseURL(newSettings.connection.serverURL)
        
        // Update WebSocket
        if let wsURL = newSettings.connection.websocketURL {
            socketManager.updateURL(wsURL)
        }
        
        // Restart connection if needed
        if isConnected && (
            settings.connection.serverURL != newSettings.connection.serverURL ||
            settings.connection.websocketURL != newSettings.connection.websocketURL
        ) {
            Task {
                disconnectFromServer()
                await connectToServer()
            }
        }
    }
    
    private func loadSettings() {
        if let savedSettings = preferencesManager.loadSettings() {
            self.settings = savedSettings
            // Update clients with new settings
            apiClient.updateBaseURL(savedSettings.connection.serverURL)
            if let wsURL = savedSettings.connection.websocketURL {
                socketManager.updateURL(wsURL)
            }
        }
    }
    
    private func saveSettings() {
        preferencesManager.saveSettings(settings)
    }
    
    // MARK: - Search
    
    private func setupSearchBinding() {
        $searchQuery
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] query in
                Task { @MainActor in
                    await self?.performSearch(query)
                }
            }
            .store(in: &cancellables)
    }
    
    private func performSearch(_ query: String) async {
        guard !query.isEmpty else {
            searchResults = []
            return
        }
        
        let lowercaseQuery = query.lowercased()
        var results: [SearchResult] = []
        
        // Search services
        for service in services {
            if service.name.lowercased().contains(lowercaseQuery) {
                results.append(SearchResult(
                    title: service.name,
                    subtitle: "Service - \(service.status.rawValue.capitalized)",
                    category: .services,
                    action: {
                        self.selectedService = service
                        self.currentView = .services
                    }
                ))
            }
        }
        
        // Search Docker containers
        for container in dockerContainers {
            if container.name.lowercased().contains(lowercaseQuery) {
                results.append(SearchResult(
                    title: container.name,
                    subtitle: "Container - \(container.status.rawValue.capitalized)",
                    category: .containers,
                    action: {
                        self.selectedContainer = container
                        self.currentView = .docker
                    }
                ))
            }
        }
        
        searchResults = results
    }
    
    // MARK: - Private Methods
    
    private func setupNotificationObservers() {
        NotificationCenter.default.publisher(for: .showQuickSearch)
            .sink { [weak self] _ in
                self?.showingQuickSearch = true
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: .refreshAllData)
            .sink { [weak self] _ in
                Task {
                    await self?.refreshAllData()
                }
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: .connectToServer)
            .sink { [weak self] _ in
                Task {
                    await self?.connectToServer()
                }
            }
            .store(in: &cancellables)
    }
    
    private var refreshTimer: Timer?
    
    private func setupPeriodicRefresh() {
        cancelPeriodicRefresh()
        
        refreshTimer = Timer.scheduledTimer(withTimeInterval: settings.monitoring.refreshInterval, repeats: true) { [weak self] _ in
            Task {
                await self?.refreshAllData()
            }
        }
    }
    
    private func cancelPeriodicRefresh() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }
}

// MARK: - Supporting Enums

enum ConnectionStatus {
    case disconnected
    case connecting
    case connected
    case error
}

enum AppView {
    case dashboard
    case services
    case docker
    case logs
    case systemMetrics
    case notifications
    case settings
}

enum APIError: Error, LocalizedError {
    case connectionFailed(String)
    case invalidResponse
    case unauthorized
    case serverError(String)
    
    var errorDescription: String? {
        switch self {
        case .connectionFailed(let message):
            return "Connection failed: \(message)"
        case .invalidResponse:
            return "Invalid response from server"
        case .unauthorized:
            return "Unauthorized access"
        case .serverError(let message):
            return "Server error: \(message)"
        }
    }
}